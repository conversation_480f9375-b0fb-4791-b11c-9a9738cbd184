/**
 * 请求工具类
 * 基于 umi-request 封装，支持双阶段认证
 */

import { message } from 'antd';
import { extend } from 'umi-request';
import type { ApiResponse } from '@/types/api';
 

// 创建请求实例
const request = extend({
  prefix: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return localStorage.getItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(TokenManager.TOKEN_KEY, token);
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    localStorage.removeItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 检查是否有Token
   */
  static hasToken(): boolean {
    return !!TokenManager.getToken();
  }
}

/**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */
request.interceptors.request.use((url, options) => {
  const token = TokenManager.getToken();

  if (token) {
    // 添加Authorization头部
    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
    };
    return {
      url,
      options: { ...options, headers },
    };
  }

  return { url, options };
});

/**
 * HTTP状态码错误消息映射
 * 为常见的HTTP状态码提供用户友好的中文错误消息
 */
const HTTP_ERROR_MESSAGES: Record<number, string> = {
  400: '请求参数错误',
  401: '登录已过期，请重新登录',
  403: '没有权限访问该资源',
  404: '请求的资源不存在',
  405: '请求方法不被允许',
  408: '请求超时',
  409: '请求冲突',
  422: '请求数据验证失败',
  429: '请求过于频繁，请稍后重试',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务暂时不可用',
  504: '网关超时',
};

/**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 处理HTTP 200状态码下的业务错误（code !== 200）
 * - 处理非200 HTTP状态码的错误情况
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 */
// 统一的响应拦截器
request.interceptors.response.use((response: any) => {
  // 对于umi-request，响应数据在response.data中
  const data = response.data;

  // 处理HTTP 200状态码的情况
  if (response.status === 200) {
    // 检查API响应是否包含业务错误（code字段不等于200）
    if (data?.code !== undefined && data.code !== 200) {
      console.log('检测到API业务错误:', data);

      // 使用Ant Design message组件显示错误消息
      const errorMessage = data.message || '请求失败';
      message.error(errorMessage);

      // 抛出错误以便调用方知道请求失败
      throw new Error(errorMessage);
    }

    // 成功情况，返回响应
    return response;
  }

  // 处理非200 HTTP状态码的情况
  // 这种情况通常不会到达这里，而是会进入catch处理
  // 但为了完整性，我们也处理一下
  const errorMessage = data?.message || HTTP_ERROR_MESSAGES[response.status] || `HTTP错误 ${response.status}`;
  message.error(errorMessage);
  throw new Error(errorMessage);
});

// 配置全局错误处理器
request.extendOptions({
  errorHandler: (error: any) => {
    // 处理网络错误或HTTP非200状态码错误
    if (error.response) {
      const { status, data } = error.response;

      // 特殊处理401认证失效
      if (status === 401) {
        TokenManager.clearToken();
        message.error('登录已过期，请重新登录');
        if (window.location.pathname !== '/user/login') {
          window.location.href = '/user/login';
        }
        return;
      }

      // 特殊处理403权限错误
      if (status === 403) {
        // 检查是否是团队访问被拒绝的特殊错误
        const errorMessage = data?.message;
        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {
          // 团队访问相关的错误，使用后端返回的具体消息
          message.error(errorMessage);
        } else {
          // 其他权限错误，使用默认消息
          message.error(HTTP_ERROR_MESSAGES[403]);
        }
        return;
      }

      // 处理其他HTTP状态码错误
      let errorMessage: string;

      // 优先使用后端返回的错误消息
      if (data?.message) {
        errorMessage = data.message;
      } else {
        // 使用预定义的错误消息映射
        errorMessage = HTTP_ERROR_MESSAGES[status] || `请求失败 (${status})`;
      }

      message.error(errorMessage);
    } else if (error.request) {
      // 请求已发出但没有收到响应（网络错误）
      message.error('网络错误，请检查网络连接');
    } else {
      // 其他错误（请求配置错误等）
      message.error(error.message || '请求失败');
    }
  },
});

// 封装常用的请求方法
export const apiRequest = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params });
  },

  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, { data });
  },

  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, { data });
  },

  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params });
  },
};

// 导出 Token 管理器
export { TokenManager };

// 导出默认请求实例
export default request;
